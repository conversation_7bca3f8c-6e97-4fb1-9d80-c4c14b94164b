import React from "react";
import { useSelector } from "react-redux";
import {
  getDepartmentsByCompany,
  getPeopleByCompany,
  getVesselsByCompany,
} from "../components/contacts/helpers/contactFilters";

// Components
import GeneralContactScreen from "../components/contacts/GeneralContactScreen";

const ContactDetailsHOC: React.FC = () => {
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const companyGuid = "fdb15c55-3f69-f011-be0c-6045bd2aaf50";

  const departmentsList = getDepartmentsByCompany(contacts, companyGuid).map(
    (id: string) => contacts.byId[id]
  );
  const peopleList = getPeopleByCompany(contacts, companyGuid).map(
    (id: string) => contacts.byId[id]
  );
  const vesselsList = getVesselsByCompany(contacts, companyGuid).map(
    (id: string) => contacts.byId[id]
  );


  return (
    <GeneralContactScreen
      departmentsList={departmentsList}
      peopleList={peopleList}
      vesselsList={vesselsList}
      companyGuid={companyGuid}
    />
  );
};

export default ContactDetailsHOC;
