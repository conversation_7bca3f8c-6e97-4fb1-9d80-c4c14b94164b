export type Contact = {
  CNT_Guid: string;
  CNT_CNT_Guid: string | null;
  CNT_CNT_DisplayName: string | null;
  CNT_Type: number;
  CNT_Prefix: string | null;
  CNT_FirstName: string | null;
  CNT_MiddleName: string | null;
  CNT_LastName: string | null;
  CNT_DisplayName: string | null;
  CNT_ImportId: string | null;
  ADR_Street: string | null;
  ADR_COU_Guid: string | null;
  ADR_CIT_Guid: string | null;
  CIT_Name: string | null;
  ADR_State: string | null;
  ADR_PostCode: string | null;
  COU_Name: string | null;
  CNT_Notes: string | null;
  CNT_CreatedUserGuid: string;
  CNT_UpdatedUserGuid: string;
  Classifications: any[];
  FLN_EntityPK_Guid_Temp: string | null;
  FLN_Guid: string | null;
  companyGuid: string | null;
  departmentGuid: string | null;
  CNT_UserAccessLevel: string | null;
  restrictedPaticipants: string | null;
  contactingValue: string | null;
  contactingDisplayName: string | null;
  CNT_ImportedTablePK_Val: string | null;
  CNT_MaxEmailMessageSizeMb: number | null;
  CNT_Abbreviation: string | null;
};
