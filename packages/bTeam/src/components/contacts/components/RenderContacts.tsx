import { Pressable, View, StyleSheet } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  IconButton,
  SPACING,
} from "b-ui-lib";

type Props = {
  contactsList: string[];
  onPress: () => void;
};

const RenderContacts = ({ contactsList, onPress }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <Pressable style={styles.contactsRow} onPress={onPress}>
      <View style={styles.contactsInfo}>
        <CustomText>{contactsList.length}</CustomText>
        <IconButton name="user" size={16} color={color.TEXT_DEFAULT} />
      </View>

      <View>
        <IconButton name="chevron-right" size={16} color={color.TEXT_DEFAULT} />
      </View>
    </Pressable>
  );
};

export default RenderContacts;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    contactsRow: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    contactsInfo: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.XS,
    },
  });

  return { styles, color };
};
