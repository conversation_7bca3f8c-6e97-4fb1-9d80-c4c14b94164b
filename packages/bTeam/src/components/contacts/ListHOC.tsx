import React from "react";
import { useNavigation, useRoute } from "@react-navigation/native";
import { FlatList, Pressable, StyleSheet, Text, View } from "react-native";
import { useSelector } from "react-redux";

import { type Theme, useThemeAwareObject } from "b-ui-lib";
import { Contact } from "../../types/contact";
import { SCREEN_NAMES } from "../../constants/screenNames";
import List from "./List";

type Props = {};

const ListHOC = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const route = useRoute();
  const contactGuids = route.params?.contactGuids;
  const data = contactGuids.map((id: string) => contacts.byId[id]);

  return (
    <View style={styles.container}>
      <List data={data} />
    </View>
  );
};

export default ListHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
