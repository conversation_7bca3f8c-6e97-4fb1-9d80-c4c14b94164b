import { useState, useCallback } from "react";
import { StyleSheet, View, Pressable } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  BenefitIconSet,
  SPACING,
} from "b-ui-lib";

// Components
import CompanyDetailsScreen from "./CompanyDetailsScreen";

// Types
import { Contact } from "../../types/contact";
import List from "./List";

type Props = {
  departmentsList: Contact[];
  peopleList: Contact[];
  vesselsList: Contact[];
  navigateToScreen: (contactId: string) => void;
  companyGuid: string;
};

type Tab = {
  key: string;
  testID: string;
  iconName: string;
  component: () => JSX.Element;
};

type CustomTabBarProps = {
  activeTab: number;
  onTabPress: (index: number) => void;
  tabs: Tab[];
  color: any;
};

// Custom tab bar component
function CustomTabBar({
  activeTab,
  onTabPress,
  tabs,
  color,
}: CustomTabBarProps) {
  return (
    <View
      style={{
        flexDirection: "row",
        backgroundColor: color.BORDER_EMAIL_FOLDER,
      }}
    >
      {tabs.map((tab, index) => {
        const isFocused = activeTab === index;

        return (
          <Pressable
            key={tab.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            testID={tab.testID}
            onPress={() => onTabPress(index)}
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              height: 75,
              backgroundColor: isFocused
                ? color.MESSAGE_ITEM__BACKGROUND
                : color.BORDER_EMAIL_FOLDER,
              borderWidth: 1,
              borderRightColor: color.BORDER_EMAIL_FOLDER,
              borderLeftColor: color.BORDER_EMAIL_FOLDER,
              borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
              borderTopWidth: 3,
              borderTopColor: isFocused
                ? color.MESSAGE_FLAG
                : color.BORDER_EMAIL_FOLDER,
              elevation: 0,
              shadowOpacity: 0,
            }}
          >
            <BenefitIconSet
              name={tab.iconName}
              size={24}
              color={isFocused ? color.MESSAGE_FLAG : color.TEXT_DIMMED}
            />
          </Pressable>
        );
      })}
    </View>
  );
}

const GeneralContactScreen = ({
  departmentsList,
  peopleList,
  companyGuid,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [activeTab, setActiveTab] = useState(0);

  // Define tabs configuration
  const tabs = [
    {
      key: "company",
      testID: "companyTab",
      iconName: "anchor",
      component: () => (
        <CompanyDetailsScreen
          companyGuid={companyGuid}
          peopleList={peopleList}
        />
      ),
    },
    {
      key: "department",
      testID: "departmentTab",
      iconName: "dashboard",
      component: () => <List data={departmentsList} />,
    },
  ];

  // Handle tab press
  const handleTabPress = useCallback(
    (tabIndex: number) => {
      if (tabIndex === activeTab) return;
      setActiveTab(tabIndex);
    },
    [activeTab]
  );

  // Render tab content
  const renderTabContent = useCallback(() => {
    return tabs[activeTab].component();
  }, [activeTab, tabs]);

  return (
    <View style={styles.container}>
      <CustomTabBar
        activeTab={activeTab}
        onTabPress={handleTabPress}
        tabs={tabs}
        color={color}
      />
      <View style={{ flex: 1 }}>{renderTabContent()}</View>
    </View>
  );
};

export default GeneralContactScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      paddingBottom: SPACING.L,
    },
  });

  return { styles, color };
};
