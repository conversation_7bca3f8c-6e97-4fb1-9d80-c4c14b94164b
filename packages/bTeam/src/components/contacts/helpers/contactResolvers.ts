import { ContactType } from './contactTypes';

export interface Message {
  CNT_Guid: string;
  CNT_CNT_Guid?: string;
  CNT_Type: ContactType;
  CNT_DisplayName: string;
  CNT_Prefix?: string;
  CNT_Notes?: string;
  Classifications: Array<{ CLA_Name: string }>;
}

export interface MessagesState {
  byId: { [key: string]: Message };
  allIds: string[];
}

export interface AddressesState {
  byId: { [key: string]: any };
  allIds: string[];
}

export const resolveDepartmentName = (
  message: Message,
  messages: MessagesState
): string | undefined => {
  const parent = message.CNT_CNT_Guid
    ? messages.byId[message.CNT_CNT_Guid]
    : undefined;
  return parent?.CNT_Type === ContactType.Department
    ? parent.CNT_DisplayName
    : undefined;
};

export const resolveCompanyName = (
  message: Message,
  messages: MessagesState
): string | undefined => {
  const parent = message.CNT_CNT_Guid
    ? messages.byId[message.CNT_CNT_Guid]
    : undefined;

  if (parent?.CNT_Type === ContactType.Department && parent.CNT_CNT_Guid) {
    const company = messages.byId[parent.CNT_CNT_Guid];
    return company?.CNT_DisplayName;
  }

  if (parent?.CNT_Type === ContactType.Company) {
    return parent.CNT_DisplayName;
  }

  return undefined;
};

export const resolveAddresses = (message: any, addresses: any) => {
  // addresses map allids and check where ADR_CNT_Guid === message.CNT_Guid
  return addresses.allIds
    .map((id: string) => addresses.byId[id])
    .filter((address: any) => address.ADR_CNT_Guid === message.CNT_Guid);
};

export const resolveAddressesWithLocation = (
  message: any,
  addresses: any,
  countries: any,
  cities: any
) => {
  // addresses map allids and check where ADR_CNT_Guid === message.CNT_Guid
  // the address should look like this: 34, Dorikou st 151 22 Marousi Athens, Greece

  return addresses.allIds
    .map((id: string) => addresses.byId[id])
    .filter((address: any) => address.ADR_CNT_Guid === message.CNT_Guid)
    .map((address: any) => {
      const addressStreet = address.ADR_Street ?? "";
      const addressPostCode = address.ADR_PostCode ?? "";
      const city = cities.byId[address.ADR_CIT_Guid]?.CIT_Name ?? "";
      const country = countries.byId[address.ADR_COU_Guid]?.COU_Name ?? "";

      return `${addressStreet}, ${addressPostCode} ${city}, ${country}`;
    });
};
