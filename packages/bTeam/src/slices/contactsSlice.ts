import { createSlice } from "@reduxjs/toolkit";
import { CountryDTO } from "../types/DTOs/CountryDTO";
import { CityDTO } from "../types/DTOs/CityDTO";
import { ClassificationDTO } from "../types/DTOs/ClassificationDTO";
import { ContactTypeDTO } from "../types/DTOs/ContactTypeDTO";
import { ContactingTypeDTO } from "../types/DTOs/ContactingTypeDTO";
import { VesselTypeDTO } from "../types/DTOs/VesselTypeDTO";
import { ContactDTO } from "../types/DTOs/ContactDTO";
import { AddressDTO } from "../types/DTOs/AddressDTO";
import { ContactClassificationDTO } from "../types/DTOs/ContactClassificationDTO";
import { ContactingDTO } from "../types/DTOs/ContactingDTO";
import { Country } from "../types/country";
import { City } from "../types/city";
import { Classification } from "../types/classification";
import { ContactType } from "../types/contactType";
import { ContactingType } from "../types/contactingType";
import { VesselType } from "../types/vesselType";
import { Contact } from "../types/contact";
import { Address } from "../types/address";
import { ContactClassification } from "../types/contactClassification";
import { Contacting } from "../types/contacting";
import { mapCountry } from "../helpers/mapCountry";
import { mapCity } from "../helpers/mapCity";
import { mapClassification } from "../helpers/mapClassification";
import { mapContactType } from "../helpers/mapContactType";
import { mapContactingType } from "../helpers/mapContactingType";
import { mapVesselType } from "../helpers/mapVesselType";
import { mapContact } from "../helpers/mapContact";
import { mapAddress } from "../helpers/mapAddress";
import { mapContactClassification } from "../helpers/mapContactClassification";
import { mapContacting } from "../helpers/mapContacting";

type ContactsState = {
  countries: {
    byId: Record<string, Country>;
    allIds: string[];
  };
  countriesIsLoading: boolean;
  countriesErrorText: string | null;

  cities: {
    byId: Record<string, City>;
    allIds: string[];
  };
  citiesIsLoading: boolean;
  citiesErrorText: string | null;

  classifications: {
    byId: Record<string, Classification>;
    allIds: string[];
  };
  classificationsIsLoading: boolean;
  classificationsErrorText: string | null;

  contactTypes: {
    byId: Record<string, ContactType>;
    allIds: string[];
  };
  contactTypesIsLoading: boolean;
  contactTypesErrorText: string | null;

  contactingTypes: {
    byId: Record<string, ContactingType>;
    allIds: string[];
  };
  contactingTypesIsLoading: boolean;
  contactingTypesErrorText: string | null;

  vesselTypes: {
    byId: Record<string, VesselType>;
    allIds: string[];
  };
  vesselTypesIsLoading: boolean;
  vesselTypesErrorText: string | null;

  contacts: {
    byId: Record<string, Contact>;
    allIds: string[];
  };
  contactsIsLoading: boolean;
  contactsErrorText: string | null;

  addresses: {
    byId: Record<string, Address>;
    allIds: string[];
  };
  addressesIsLoading: boolean;
  addressesErrorText: string | null;

  contactClassifications: {
    byId: Record<string, ContactClassification>;
    allIds: string[];
  };
  contactClassificationsIsLoading: boolean;
  contactClassificationsErrorText: string | null;

  contacting: {
    byId: Record<string, Contacting>;
    allIds: string[];
  };
  contactingIsLoading: boolean;
  contactingErrorText: string | null;
};

const initialState: ContactsState = {
  countries: {
    byId: {},
    allIds: [],
  },
  cities: {
    byId: {},
    allIds: [],
  },
  classifications: {
    byId: {},
    allIds: [],
  },
  contactTypes: {
    byId: {},
    allIds: [],
  },
  contactingTypes: {
    byId: {},
    allIds: [],
  },
  vesselTypes: {
    byId: {},
    allIds: [],
  },
  contacts: {
    byId: {},
    allIds: [],
  },
  addresses: {
    byId: {},
    allIds: [],
  },
  contactClassifications: {
    byId: {},
    allIds: [],
  },
  contacting: {
    byId: {},
    allIds: [],
  },
  countriesIsLoading: false,
  countriesErrorText: null,
  citiesIsLoading: false,
  citiesErrorText: null,
  classificationsIsLoading: false,
  classificationsErrorText: null,
  contactTypesIsLoading: false,
  contactTypesErrorText: null,
  contactingTypesIsLoading: false,
  contactingTypesErrorText: null,
  vesselTypesIsLoading: false,
  vesselTypesErrorText: null,
  contactsIsLoading: false,
  contactsErrorText: null,
  addressesIsLoading: false,
  addressesErrorText: null,
  contactClassificationsIsLoading: false,
  contactClassificationsErrorText: null,
  contactingIsLoading: false,
  contactingErrorText: null,
};

const contactsSlice = createSlice({
  name: "bTeamContactsSlice",
  initialState,
  reducers: {
    getCountries: (state) => {
      state.countriesIsLoading = true;
      state.countriesErrorText = null;
    },
    getCountriesSuccess: (state, { payload }) => {
      const { data: countries, isLastPage }: { data: CountryDTO[], isLastPage?: boolean } = payload;
      const countriesById: Record<string, Country> = {};
      const allIds: string[] = [];

      countries.forEach((country) => {
        const mappedCountry = mapCountry(country);
        const id = mappedCountry.COU_Guid;
        countriesById[id] = mappedCountry;
        allIds.push(id);
      });

      state.countries.byId = { ...state.countries.byId, ...countriesById };
      state.countries.allIds = Array.from(
        new Set([...state.countries.allIds, ...allIds])
      );
      if (isLastPage) {
        state.countriesIsLoading = false;
      }
      state.countriesErrorText = null;
    },
    getCountriesFailed: (state, { payload }) => {
      state.countriesIsLoading = false;
      state.countriesErrorText = payload;
    },
    getCities: (state) => {
      state.citiesIsLoading = true;
      state.citiesErrorText = null;
    },
    getCitiesSuccess: (state, { payload }) => {
      const { data: cities, isLastPage }: { data: CityDTO[], isLastPage?: boolean } = payload;
      const citiesById: Record<string, City> = {};
      const allIds: string[] = [];

      cities.forEach((city) => {
        const mappedCity = mapCity(city);
        const id = mappedCity.CIT_Guid;
        citiesById[id] = mappedCity;
        allIds.push(id);
      });

      state.cities.byId = { ...state.cities.byId, ...citiesById };
      state.cities.allIds = Array.from(
        new Set([...state.cities.allIds, ...allIds])
      );
      if (isLastPage) {
        state.citiesIsLoading = false;
      }
      state.citiesErrorText = null;
    },
    getCitiesFailed: (state, { payload }) => {
      state.citiesIsLoading = false;
      state.citiesErrorText = payload;
    },
    getClassifications: (state) => {
      state.classificationsIsLoading = true;
      state.classificationsErrorText = null;
    },
    getClassificationsSuccess: (state, { payload }) => {
      const { data: classifications, isLastPage }: { data: ClassificationDTO[], isLastPage?: boolean } = payload;
      const classificationsById: Record<string, Classification> = {};
      const allIds: string[] = [];

      classifications.forEach((classification) => {
        const mappedClassification = mapClassification(classification);
        const id = mappedClassification.CLA_Guid;
        classificationsById[id] = mappedClassification;
        allIds.push(id);
      });

      state.classifications.byId = {
        ...state.classifications.byId,
        ...classificationsById,
      };
      state.classifications.allIds = Array.from(
        new Set([...state.classifications.allIds, ...allIds])
      );
      if (isLastPage) {
        state.classificationsIsLoading = false;
      }
      state.classificationsErrorText = null;
    },
    getClassificationsFailed: (state, { payload }) => {
      state.classificationsIsLoading = false;
      state.classificationsErrorText = payload;
    },
    getContactTypes: (state) => {
      state.contactTypesIsLoading = true;
      state.contactTypesErrorText = null;
    },
    getContactTypesSuccess: (state, { payload }) => {
      const { data: contactTypes, isLastPage }: { data: ContactTypeDTO[], isLastPage?: boolean } = payload;
      const contactTypesById: Record<string, ContactType> = {};
      const allIds: string[] = [];

      contactTypes.forEach((contactType) => {
        const mappedContactType = mapContactType(contactType);
        const id = mappedContactType.CTY_Guid;
        contactTypesById[id] = mappedContactType;
        allIds.push(id);
      });

      state.contactTypes.byId = {
        ...state.contactTypes.byId,
        ...contactTypesById,
      };
      state.contactTypes.allIds = Array.from(
        new Set([...state.contactTypes.allIds, ...allIds])
      );
      if (isLastPage) {
        state.contactTypesIsLoading = false;
      }
      state.contactTypesErrorText = null;
    },
    getContactTypesFailed: (state, { payload }) => {
      state.contactTypesIsLoading = false;
      state.contactTypesErrorText = payload;
    },
    getContactingTypes: (state) => {
      state.contactingTypesIsLoading = true;
      state.contactingTypesErrorText = null;
    },
    getContactingTypesSuccess: (state, { payload }) => {
      const { data: contactingTypes, isLastPage }: { data: ContactingTypeDTO[], isLastPage?: boolean } = payload;
      const contactingTypesById: Record<string, ContactingType> = {};
      const allIds: string[] = [];

      contactingTypes.forEach((contactingType) => {
        const mappedContactingType = mapContactingType(contactingType);
        const id = mappedContactingType.CTT_Guid;
        contactingTypesById[id] = mappedContactingType;
        allIds.push(id);
      });

      state.contactingTypes.byId = {
        ...state.contactingTypes.byId,
        ...contactingTypesById,
      };
      state.contactingTypes.allIds = Array.from(
        new Set([...state.contactingTypes.allIds, ...allIds])
      );
      if (isLastPage) {
        state.contactingTypesIsLoading = false;
      }
      state.contactingTypesErrorText = null;
    },
    getContactingTypesFailed: (state, { payload }) => {
      state.contactingTypesIsLoading = false;
      state.contactingTypesErrorText = payload;
    },
    getVesselTypes: (state) => {
      state.vesselTypesIsLoading = true;
      state.vesselTypesErrorText = null;
    },
    getVesselTypesSuccess: (state, { payload }) => {
      const { data: vesselTypes, isLastPage }: { data: VesselTypeDTO[], isLastPage?: boolean } = payload;
      const vesselTypesById: Record<string, VesselType> = {};
      const allIds: string[] = [];

      vesselTypes.forEach((vesselType) => {
        const mappedVesselType = mapVesselType(vesselType);
        const id = mappedVesselType.VST_Guid;
        vesselTypesById[id] = mappedVesselType;
        allIds.push(id);
      });

      state.vesselTypes.byId = {
        ...state.vesselTypes.byId,
        ...vesselTypesById,
      };
      state.vesselTypes.allIds = Array.from(
        new Set([...state.vesselTypes.allIds, ...allIds])
      );
      if (isLastPage) {
        state.vesselTypesIsLoading = false;
      }
      state.vesselTypesErrorText = null;
    },
    getVesselTypesFailed: (state, { payload }) => {
      state.vesselTypesIsLoading = false;
      state.vesselTypesErrorText = payload;
    },
    getContacts: (state) => {
      state.contactsIsLoading = true;
      state.contactsErrorText = null;
    },
    getContactsSuccess: (state, { payload }) => {
      const { data: contacts, isLastPage }: { data: ContactDTO[], isLastPage?: boolean } = payload;
      const contactsById: Record<string, Contact> = {};
      const allIds: string[] = [];

      contacts.forEach((contact) => {
        const mappedContact = mapContact(contact);
        const id = mappedContact.CNT_Guid;
        contactsById[id] = mappedContact;
        allIds.push(id);
      });

      state.contacts.byId = { ...state.contacts.byId, ...contactsById };
      state.contacts.allIds = Array.from(
        new Set([...state.contacts.allIds, ...allIds])
      );
      if (isLastPage) {
        state.contactsIsLoading = false;
      }
      state.contactsErrorText = null;
    },
    getContactsFailed: (state, { payload }) => {
      state.contactsIsLoading = false;
      state.contactsErrorText = payload;
    },
    getAddresses: (state) => {
      state.addressesIsLoading = true;
      state.addressesErrorText = null;
    },
    getAddressesSuccess: (state, { payload }) => {
      const { data: addresses, isLastPage }: { data: AddressDTO[], isLastPage?: boolean } = payload;
      const addressesById: Record<string, Address> = {};
      const allIds: string[] = [];

      addresses.forEach((address) => {
        const mappedAddress = mapAddress(address);
        const id = mappedAddress.ADR_Guid;
        addressesById[id] = mappedAddress;
        allIds.push(id);
      });

      state.addresses.byId = { ...state.addresses.byId, ...addressesById };
      state.addresses.allIds = Array.from(
        new Set([...state.addresses.allIds, ...allIds])
      );
      if (isLastPage) {
        state.addressesIsLoading = false;
      }
      state.addressesErrorText = null;
    },
    getAddressesFailed: (state, { payload }) => {
      state.addressesIsLoading = false;
      state.addressesErrorText = payload;
    },
    getContactClassifications: (state) => {
      state.contactClassificationsIsLoading = true;
      state.contactClassificationsErrorText = null;
    },
    getContactClassificationsSuccess: (state, { payload }) => {
      const { data: contactClassifications, isLastPage }: { data: ContactClassificationDTO[], isLastPage?: boolean } = payload;
      const contactClassificationsById: Record<string, ContactClassification> =
        {};
      const allIds: string[] = [];

      contactClassifications.forEach((contactClassification) => {
        const mappedContactClassification = mapContactClassification(
          contactClassification
        );
        const id = mappedContactClassification.CCL_Guid;
        contactClassificationsById[id] = mappedContactClassification;
        allIds.push(id);
      });

      state.contactClassifications.byId = {
        ...state.contactClassifications.byId,
        ...contactClassificationsById,
      };
      state.contactClassifications.allIds = Array.from(
        new Set([...state.contactClassifications.allIds, ...allIds])
      );
      if (isLastPage) {
        state.contactClassificationsIsLoading = false;
      }
      state.contactClassificationsErrorText = null;
    },
    getContactClassificationsFailed: (state, { payload }) => {
      state.contactClassificationsIsLoading = false;
      state.contactClassificationsErrorText = payload;
    },
    getContacting: (state) => {
      state.contactingIsLoading = true;
      state.contactingErrorText = null;
    },
    getContactingSuccess: (state, { payload }) => {
      const { data: contacting, isLastPage }: { data: ContactingDTO[], isLastPage?: boolean } = payload;
      const contactingById: Record<string, Contacting> = {};
      const allIds: string[] = [];

      contacting.forEach((contacting) => {
        const mappedContacting = mapContacting(contacting);
        const id = mappedContacting.CTG_Guid;
        contactingById[id] = mappedContacting;
        allIds.push(id);
      });

      state.contacting.byId = { ...state.contacting.byId, ...contactingById };
      state.contacting.allIds = Array.from(
        new Set([...state.contacting.allIds, ...allIds])
      );
      if (isLastPage) {
        state.contactingIsLoading = false;
      }
      state.contactingErrorText = null;
    },
    getContactingFailed: (state, { payload }) => {
      state.contactingIsLoading = false;
      state.contactingErrorText = payload;
    },
  },
});

export const {
  getCountries,
  getCountriesSuccess,
  getCountriesFailed,
  getCities,
  getCitiesSuccess,
  getCitiesFailed,
  getClassifications,
  getClassificationsSuccess,
  getClassificationsFailed,
  getContactTypes,
  getContactTypesSuccess,
  getContactTypesFailed,
  getContactingTypes,
  getContactingTypesSuccess,
  getContactingTypesFailed,
  getVesselTypes,
  getVesselTypesSuccess,
  getVesselTypesFailed,
  getContacts,
  getContactsSuccess,
  getContactsFailed,
  getAddresses,
  getAddressesSuccess,
  getAddressesFailed,
  getContactClassifications,
  getContactClassificationsSuccess,
  getContactClassificationsFailed,
  getContacting,
  getContactingSuccess,
  getContactingFailed,
} = contactsSlice.actions;

export default contactsSlice.reducer;
