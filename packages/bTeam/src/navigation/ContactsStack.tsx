import React from "react";
import { Platform } from "react-native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { getHeaderTitle } from "@react-navigation/elements";
import { SCREEN_NAMES } from "../constants/screenNames";

// Components
import ContactsListHOC from "../containers/ContactsListHOC";
import ContactsListHeader from "./headers/ContactsListHeader";
import ContactDetailsHOC from "../containers/ContactDetailsHOC";
import DepartmentDetailsScreen from "../components/contacts/DepartmentDetailsScreen";
import ContactDetailsScreen from "../components/contacts/ContactDetailsScreen";
import VesselDetailsScreen from "../components/contacts/VesselDetailsScreen";
import ListHOC from "../components/contacts/ListHOC";
import ContactsDetailsHeader from "./headers/ContactsDetailsHeader";

const Stack = createNativeStackNavigator();

const ContactsStack: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName={SCREEN_NAMES.contacts}
      screenOptions={{
        presentation: "card",
        animationTypeForReplace: "push",
        animation: Platform.OS === "ios" ? "none" : "slide_from_right",
      }}
    >
      {/* <Stack.Screen
        name={SCREEN_NAMES.contacts}
        component={ContactsListHOC}
        options={{
          header: ({ navigation, route, options }) => {
            const title = getHeaderTitle(options, route.name);

            return <ContactsListHeader navigation={navigation} title={title} />;
          },
        }}
      /> */}

      <Stack.Screen
        name={SCREEN_NAMES.generalContactDetails}
        component={ContactDetailsHOC}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <ContactsDetailsHeader
                navigation={navigation}
                title={options?.companyName || title}
                back={back}
              />
            );
          },
        }}
      />

      <Stack.Screen
        name={SCREEN_NAMES.departmentDetails}
        component={DepartmentDetailsScreen}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <ContactsDetailsHeader
                navigation={navigation}
                title={options?.title || title}
                back={back}
              />
            );
          },
        }}
      />

      <Stack.Screen
        name={SCREEN_NAMES.contactDetails}
        component={ContactDetailsScreen}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <ContactsDetailsHeader navigation={navigation} back={back} />
            );
          },
        }}
      />

      <Stack.Screen
        name={SCREEN_NAMES.vesselDetails}
        component={VesselDetailsScreen}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <ContactsDetailsHeader
                navigation={navigation}
                title={options?.title || title}
                back={back}
              />
            );
          },
        }}
      />

      <Stack.Screen
        name={SCREEN_NAMES.contactsList}
        component={ListHOC}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <ContactsDetailsHeader
                navigation={navigation}
                title={"Contacts"}
                back={back}
              />
            );
          },
        }}
      />
    </Stack.Navigator>
  );
};

export default ContactsStack;
