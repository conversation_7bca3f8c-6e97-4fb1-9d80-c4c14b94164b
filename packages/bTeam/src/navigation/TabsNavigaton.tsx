import React from "react";
import { StyleSheet, View } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import {
  type Theme,
  SPACING,
  useThemeAwareObject,
  BenefitIconSet,
} from "b-ui-lib";
import { SCREEN_NAMES } from "../constants/screenNames";

// Components
import MenuHOC from "../containers/MenuHOC";
import InboxStack from "./InboxStack";
import NotificationsStack from "./NotificationsStack";
import CasesStack from "./CasesStack";
import ContactsStack from "./ContactsStack";

const Tab = createBottomTabNavigator();

const TabsNavigation = () => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const tabBarItemStyle = {
    paddingVertical: SPACING.XXS,
    borderRadius: 6,
  };

  return (
    <View style={styles.container}>
      <Tab.Navigator
        initialRouteName={SCREEN_NAMES.inboxStack}
        screenOptions={{
          tabBarStyle: {
            backgroundColor: color.TAB_BACKGROUND,
            minHeight: 50,
            elevation: 0,
            paddingHorizontal: SPACING.XS,
            paddingTop: SPACING.XXS,
            borderColor: color.BORDER_EMAIL_FOLDER,
          },
          tabBarLabelStyle: { fontSize: 12 },
          tabBarActiveTintColor: color.MESSAGE_FLAG,
          tabBarInactiveTintColor: color.TEXT_DIMMED,
          tabBarActiveBackgroundColor: color.TAB_BACKGROUND_ACTIVE,
          headerShown: false,
        }}
      >
        <Tab.Screen
          name={SCREEN_NAMES.inboxStack}
          component={InboxStack}
          options={{
            title: "Email",
            tabBarIcon: ({ focused }) => (
              <BenefitIconSet
                name="mail"
                size={20}
                color={focused ? color.MESSAGE_FLAG : color.TEXT_DEFAULT}
              />
            ),
            tabBarItemStyle,
          }}
        />

        {/*<Tab.Screen*/}
        {/*  name={SCREEN_NAMES.notificationsStack}*/}
        {/*  component={NotificationsStack}*/}
        {/*  options={{*/}
        {/*    title: "Notifications",*/}
        {/*    tabBarIcon: ({ focused }) => (*/}
        {/*      <BenefitIconSet*/}
        {/*        name="message"*/}
        {/*        size={20}*/}
        {/*        color={focused ? color.MESSAGE_FLAG : color.TEXT_DEFAULT}*/}
        {/*      />*/}
        {/*    ),*/}
        {/*    tabBarItemStyle,*/}
        {/*  }}*/}
        {/*/>*/}

        <Tab.Screen
          name={SCREEN_NAMES.casesStack}
          component={CasesStack}
          options={{
            title: "Cases",
            tabBarIcon: ({ focused }) => (
              <BenefitIconSet
                name="check-circle"
                size={20}
                color={focused ? color.MESSAGE_FLAG : color.TEXT_DEFAULT}
              />
            ),
            tabBarItemStyle,
          }}
        />

        <Tab.Screen
          name={SCREEN_NAMES.contacts}
          component={ContactsStack}
          options={{
            title: "Contacts",
            tabBarIcon: ({ focused }) => (
              <BenefitIconSet
                name="user"
                size={20}
                color={focused ? color.MESSAGE_FLAG : color.TEXT_DEFAULT}
              />
            ),
            tabBarItemStyle,
          }}
        />

        <Tab.Screen
          name={SCREEN_NAMES.menu}
          component={MenuHOC}
          options={{
            title: "Menu",
            tabBarIcon: ({ focused }) => (
              <BenefitIconSet
                name="more-horizontal"
                size={20}
                color={focused ? color.MESSAGE_FLAG : color.TEXT_DEFAULT}
              />
            ),
            tabBarItemStyle,
          }}
        />
      </Tab.Navigator>
    </View>
  );
};

export default TabsNavigation;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.HEADER_COLOR,
    },
  });

  return { styles, color };
};
